{"version": 3, "file": "hash-to-curve.js", "sourceRoot": "", "sources": ["../src/abstract/hash-to-curve.ts"], "names": [], "mappings": ";;;AAEA,6CAA2C;AAC3C,yCAA8F;AAoB9F,SAAS,WAAW,CAAC,GAAmB;IACtC,IAAI,GAAG,YAAY,UAAU;QAAE,OAAO,GAAG,CAAC;IAC1C,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,IAAA,sBAAW,EAAC,GAAG,CAAC,CAAC;IACrD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AACtD,CAAC;AAED,6FAA6F;AAC7F,MAAM,KAAK,GAAG,0BAAe,CAAC;AAE9B,4CAA4C;AAC5C,SAAS,KAAK,CAAC,KAAa,EAAE,MAAc;IAC1C,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,WAAW,MAAM,EAAE,CAAC,CAAC;KACpE;IACD,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAa,CAAC;IACvD,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QACpC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;QACtB,KAAK,MAAM,CAAC,CAAC;KACd;IACD,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,MAAM,CAAC,CAAa,EAAE,CAAa;IAC1C,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACtB;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,OAAO,CAAC,IAAa;IAC5B,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC5E,CAAC;AACD,SAAS,KAAK,CAAC,IAAa;IAC1B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACtE,CAAC;AAED,oGAAoG;AACpG,uDAAuD;AACvD,SAAgB,kBAAkB,CAChC,GAAe,EACf,GAAe,EACf,UAAkB,EAClB,CAAQ;IAER,OAAO,CAAC,GAAG,CAAC,CAAC;IACb,OAAO,CAAC,GAAG,CAAC,CAAC;IACb,KAAK,CAAC,UAAU,CAAC,CAAC;IAClB,uDAAuD;IACvD,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG;QAAE,GAAG,GAAG,CAAC,CAAC,IAAA,sBAAW,EAAC,IAAA,sBAAW,EAAC,mBAAmB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAClF,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;IAC1D,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC;IAC/C,IAAI,GAAG,GAAG,GAAG;QAAE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;IACrD,MAAM,SAAS,GAAG,IAAA,sBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IACzD,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACnC,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,mBAAmB;IAC3D,MAAM,CAAC,GAAG,IAAI,KAAK,CAAa,GAAG,CAAC,CAAC;IACrC,MAAM,GAAG,GAAG,CAAC,CAAC,IAAA,sBAAW,EAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAA,sBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE;QAC7B,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAA,sBAAW,EAAC,GAAG,IAAI,CAAC,CAAC,CAAC;KAChC;IACD,MAAM,mBAAmB,GAAG,IAAA,sBAAW,EAAC,GAAG,CAAC,CAAC,CAAC;IAC9C,OAAO,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAClD,CAAC;AA1BD,gDA0BC;AAED,uFAAuF;AACvF,4DAA4D;AAC5D,iEAAiE;AACjE,kEAAkE;AAClE,uDAAuD;AACvD,SAAgB,kBAAkB,CAChC,GAAe,EACf,GAAe,EACf,UAAkB,EAClB,CAAS,EACT,CAAQ;IAER,OAAO,CAAC,GAAG,CAAC,CAAC;IACb,OAAO,CAAC,GAAG,CAAC,CAAC;IACb,KAAK,CAAC,UAAU,CAAC,CAAC;IAClB,uDAAuD;IACvD,oFAAoF;IACpF,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACrC,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,IAAA,sBAAW,EAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;KACzF;IACD,IAAI,UAAU,GAAG,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG;QACxC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,OAAO,CACL,CAAC,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;SAC5B,MAAM,CAAC,GAAG,CAAC;SACX,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAC7B,2CAA2C;SAC1C,MAAM,CAAC,GAAG,CAAC;SACX,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAC5B,MAAM,EAAE,CACZ,CAAC;AACJ,CAAC;AA3BD,gDA2BC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,GAAe,EAAE,KAAa,EAAE,OAAa;IACzE,IAAA,yBAAc,EAAC,OAAO,EAAE;QACtB,GAAG,EAAE,oBAAoB;QACzB,CAAC,EAAE,QAAQ;QACX,CAAC,EAAE,eAAe;QAClB,CAAC,EAAE,eAAe;QAClB,IAAI,EAAE,MAAM;KACb,CAAC,CAAC;IACH,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,CAAC;IACb,KAAK,CAAC,KAAK,CAAC,CAAC;IACb,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAC9B,MAAM,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACnC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,uCAAuC;IAC7E,MAAM,YAAY,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IACnC,IAAI,GAAG,CAAC,CAAC,sBAAsB;IAC/B,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,GAAG,GAAG,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;KACxD;SAAM,IAAI,MAAM,KAAK,KAAK,EAAE;QAC3B,GAAG,GAAG,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;KAC3D;SAAM,IAAI,MAAM,KAAK,gBAAgB,EAAE;QACtC,0BAA0B;QAC1B,GAAG,GAAG,GAAG,CAAC;KACX;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;KAClD;IACD,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC9B,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,MAAM,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC,CAAC,GAAG,IAAA,gBAAG,EAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1B;QACD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACV;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AArCD,sCAqCC;AAED,SAAgB,UAAU,CAAyB,KAAQ,EAAE,GAAyB;IACpF,6BAA6B;IAC7B,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACtD,OAAO,CAAC,CAAI,EAAE,CAAI,EAAE,EAAE;QACpB,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CACjD,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACxD,CAAC;QACF,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,cAAc;QACzC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,oBAAoB;QAC7D,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC,CAAC;AACJ,CAAC;AAXD,gCAWC;AAmBD,SAAgB,YAAY,CAC1B,KAA6B,EAC7B,UAAyB,EACzB,GAA0C;IAE1C,IAAI,OAAO,UAAU,KAAK,UAAU;QAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACtF,OAAO;QACL,yCAAyC;QACzC,sEAAsE;QACtE,WAAW,CAAC,GAAe,EAAE,OAAsB;YACjD,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,OAAO,EAAU,CAAC,CAAC;YAC9E,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC;YACrC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,yCAAyC;QACzC,wEAAwE;QACxE,aAAa,CAAC,GAAe,EAAE,OAAsB;YACnD,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,OAAO,EAAU,CAAC,CAAC;YACpF,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;YAC7D,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,OAAO,CAAC,CAAC;QACX,CAAC;KACF,CAAC;AACJ,CAAC;AA3BD,oCA2BC"}