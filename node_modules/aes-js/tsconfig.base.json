{"compilerOptions": {"declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "lib": ["es2015", "es5", "dom"], "module": "es6", "moduleResolution": "node16", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "preserveSymlinks": true, "preserveWatchOutput": true, "pretty": false, "rootDir": "./src.ts", "sourceMap": true, "strict": true, "target": "es2016"}, "include": ["./src.ts/*.ts"], "exclude": []}