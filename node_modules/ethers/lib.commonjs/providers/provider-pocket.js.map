{"version": 3, "file": "provider-pocket.js", "sourceRoot": "", "sources": ["../../src.ts/providers/provider-pocket.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;GAYG;AACH,gDAE2B;AAG3B,iDAAqD;AACrD,6CAAuC;AACvC,+DAAwD;AAKxD,MAAM,oBAAoB,GAAG,0BAA0B,CAAC;AAExD,SAAS,OAAO,CAAC,IAAY;IACzB,QAAQ,IAAI,EAAE;QACV,KAAK,SAAS;YACV,OAAQ,kCAAkC,CAAC;QAC/C,KAAK,QAAQ;YACT,OAAO,iCAAiC,CAAC;QAE7C,KAAK,OAAO;YACR,OAAO,mCAAmC,CAAC;QAC/C,KAAK,cAAc;YACf,OAAO,yCAAyC,CAAC;KACxD;IAED,IAAA,yBAAc,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAClE,CAAC;AAGD;;;;;;;;GAQG;AACH,MAAa,cAAe,SAAQ,qCAAe;IAE/C;;OAEG;IACM,aAAa,CAAU;IAEhC;;;OAGG;IACM,iBAAiB,CAAiB;IAE3C;;;;;OAKG;IACH,YAAY,QAAqB,EAAE,aAA6B,EAAE,iBAAiC;QAC/F,IAAI,QAAQ,IAAI,IAAI,EAAE;YAAE,QAAQ,GAAG,SAAS,CAAC;SAAE;QAC/C,MAAM,OAAO,GAAG,oBAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,aAAa,IAAI,IAAI,EAAE;YAAE,aAAa,GAAG,oBAAoB,CAAC;SAAE;QACpE,IAAI,iBAAiB,IAAI,IAAI,EAAE;YAAE,iBAAiB,GAAG,IAAI,CAAC;SAAE;QAE5D,MAAM,OAAO,GAAG,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC;QAE3C,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC;QACrF,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAEjC,IAAA,2BAAgB,EAAiB,IAAI,EAAE,EAAE,aAAa,EAAE,iBAAiB,EAAE,CAAC,CAAC;IACjF,CAAC;IAED,YAAY,CAAC,OAAe;QACxB,IAAI;YACA,OAAO,IAAI,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAClF;QAAC,OAAO,KAAK,EAAE,GAAG;QACnB,OAAO,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,UAAU,CAAC,OAAgB,EAAE,aAA6B,EAAE,iBAAiC;QAChG,IAAI,aAAa,IAAI,IAAI,EAAE;YAAE,aAAa,GAAG,oBAAoB,CAAC;SAAE;QAEpE,MAAM,OAAO,GAAG,IAAI,uBAAY,CAAC,YAAa,OAAO,CAAC,OAAO,CAAC,IAAI,CAAE,UAAW,aAAc,EAAE,CAAC,CAAC;QACjG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAEzB,IAAI,iBAAiB,EAAE;YACnB,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;SACjD;QAED,IAAI,aAAa,KAAK,oBAAoB,EAAE;YACxC,OAAO,CAAC,SAAS,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;gBACrD,IAAA,kCAAmB,EAAC,gBAAgB,CAAC,CAAC;gBACtC,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC;SACL;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,mBAAmB;QACf,OAAO,CAAC,IAAI,CAAC,aAAa,KAAK,oBAAoB,CAAC,CAAC;IACzD,CAAC;CACJ;AAnED,wCAmEC"}