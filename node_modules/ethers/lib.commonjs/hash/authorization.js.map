{"version": 3, "file": "authorization.js", "sourceRoot": "", "sources": ["../../src.ts/hash/authorization.ts"], "names": [], "mappings": ";;;AAAA,kDAAiD;AACjD,iDAA+C;AAC/C,sDAAyD;AACzD,gDAE2B;AAY3B;;GAEG;AACH,SAAgB,iBAAiB,CAAC,IAA0B;IACxD,IAAA,yBAAc,EAAC,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE,uCAAuC,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;IACjH,OAAO,IAAA,oBAAS,EAAC,IAAA,iBAAM,EAAC;QACpB,MAAM,EAAE,IAAA,oBAAS,EAAC;YACd,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,oBAAS,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA,CAAC,CAAC,IAAI;YACtD,IAAA,qBAAU,EAAC,IAAI,CAAC,OAAO,CAAC;YACxB,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,oBAAS,EAAC,IAAI,CAAC,KAAK,CAAC,CAAA,CAAC,CAAC,IAAI;SACrD,CAAC;KACL,CAAC,CAAC,CAAC;AACR,CAAC;AATD,8CASC;AAED;;;GAGG;AACH,SAAgB,mBAAmB,CAAC,IAA0B,EAAE,GAAkB;IAC9E,OAAO,IAAA,yBAAc,EAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACxD,CAAC;AAFD,kDAEC"}